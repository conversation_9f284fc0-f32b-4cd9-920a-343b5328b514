import React from 'react'
import { motion } from 'framer-motion'

const GitHubRepos = () => {
  const repositories = [
    {
      id: 1,
      name: "landtrust-blockchain",
      description: "A secure digital land registry system using AI-powered OCR and blockchain technology for immutable property records.",
      language: "TypeScript",
      stars: 15,
      forks: 3,
      topics: ["blockchain", "react", "fastapi", "ocr"],
      url: "https://github.com/shaheerabbas/landtrust-blockchain",
      updated: "2024-12-01"
    },
    {
      id: 2,
      name: "mern-ecommerce",
      description: "Full-stack e-commerce platform built with MERN stack featuring real-time inventory management and secure payments.",
      language: "JavaScript",
      stars: 28,
      forks: 8,
      topics: ["mern", "ecommerce", "react", "nodejs", "mongodb"],
      url: "https://github.com/shaheerabbas/mern-ecommerce",
      updated: "2024-11-15"
    },
    {
      id: 3,
      name: "dotnet-webapp",
      description: "Enterprise-level web application built with .NET Core, featuring role-based authentication and SQL Server integration.",
      language: "C#",
      stars: 12,
      forks: 2,
      topics: ["dotnet", "csharp", "sql-server", "enterprise"],
      url: "https://github.com/shaheerabbas/dotnet-webapp",
      updated: "2024-10-20"
    },
    {
      id: 4,
      name: "react-portfolio",
      description: "Modern, responsive portfolio website with smooth animations, built using React, TypeScript, and Framer Motion.",
      language: "TypeScript",
      stars: 35,
      forks: 12,
      topics: ["react", "typescript", "framer-motion", "portfolio"],
      url: "https://github.com/shaheerabbas/react-portfolio",
      updated: "2024-12-15"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { scale: 0.9, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'TypeScript': 'bg-blue-500',
      'JavaScript': 'bg-yellow-500',
      'C#': 'bg-purple-500',
      'Python': 'bg-green-500',
      'Java': 'bg-red-500'
    }
    return colors[language] || 'bg-gray-500'
  }

  return (
    <section id="github" className="py-20 bg-gray-100 dark:bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4"
            variants={itemVariants}
          >
            Open Source Work
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            Explore my GitHub repositories showcasing various projects and contributions to the open source community.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-2 gap-6"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {repositories.map((repo) => (
            <motion.div
              key={repo.id}
              className="bg-white dark:bg-gray-900 rounded-lg p-6 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-200 dark:border-gray-700"
              variants={cardVariants}
              whileHover={{ y: -5 }}
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center">
                  <div className="w-4 h-4 bg-gray-400 rounded-full mr-3"></div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {repo.name}
                  </h3>
                </div>
                <motion.a
                  href={repo.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clipRule="evenodd" />
                  </svg>
                </motion.a>
              </div>

              {/* Description */}
              <p className="text-gray-600 dark:text-gray-400 mb-4 leading-relaxed">
                {repo.description}
              </p>

              {/* Topics */}
              <div className="flex flex-wrap gap-2 mb-4">
                {repo.topics.map((topic, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 text-xs rounded-full"
                  >
                    {topic}
                  </span>
                ))}
              </div>

              {/* Stats */}
              <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center">
                    <div className={`w-3 h-3 rounded-full mr-1 ${getLanguageColor(repo.language)}`}></div>
                    <span>{repo.language}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">⭐</span>
                    <span>{repo.stars}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="mr-1">🍴</span>
                    <span>{repo.forks}</span>
                  </div>
                </div>
                <span>Updated {new Date(repo.updated).toLocaleDateString()}</span>
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
        >
          <motion.a
            href="https://github.com/shaheerabbas"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-lg hover:shadow-lg transition-all duration-300"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            View All Repositories
          </motion.a>
        </motion.div>
      </div>
    </section>
  )
}

export default GitHubRepos
