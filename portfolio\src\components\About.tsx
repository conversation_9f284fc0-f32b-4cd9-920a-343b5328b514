import React from 'react'
import { motion } from 'framer-motion'

const About = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  return (
    <section id="about" className="py-20 bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            About Me
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto"
            variants={itemVariants}
          />
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            className="space-y-6"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            <motion.div
              className="relative"
              variants={itemVariants}
            >
              <div className="w-64 h-64 mx-auto md:mx-0 relative">
                <div className="w-full h-full bg-gradient-to-br from-purple-500 to-pink-500 rounded-full p-1">
                  <div className="w-full h-full bg-gray-800 rounded-full flex items-center justify-center">
                    <div className="w-56 h-56 bg-gradient-to-br from-gray-600 to-gray-700 rounded-full flex items-center justify-center text-white text-6xl font-bold">
                      SSA
                    </div>
                  </div>
                </div>
                <motion.div
                  className="absolute inset-0 rounded-full border-4 border-purple-500"
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                />
              </div>
            </motion.div>
          </motion.div>

          <motion.div
            className="space-y-6"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            <motion.h3
              className="text-2xl font-semibold text-white"
              variants={itemVariants}
            >
              Hello! I'm Syed Shaheer Abbas
            </motion.h3>

            <motion.p
              className="text-gray-300 leading-relaxed"
              variants={itemVariants}
            >
              Motivated and skilled full-stack developer with hands-on experience in MERN
              and .NET technologies. I'm passionate about building efficient, scalable,
              and clean web applications with strong cross-platform logic. Currently pursuing
              my Bachelor's in Computer Science at NUML.
            </motion.p>

            <motion.p
              className="text-gray-300 leading-relaxed"
              variants={itemVariants}
            >
              When I'm not coding, you can find me organizing tech events like Hackforge
              and chess tournaments at ACM, or exploring new frameworks and technologies.
              I believe in continuous learning and love tackling challenging problems
              that create real-world impact.
            </motion.p>

            <motion.div
              className="grid grid-cols-2 gap-4"
              variants={itemVariants}
            >
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-purple-400 font-semibold mb-2">Education</h4>
                <p className="text-gray-300 text-sm">BS Computer Science, NUML</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-purple-400 font-semibold mb-2">Focus</h4>
                <p className="text-gray-300 text-sm">MERN & .NET Stack</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-purple-400 font-semibold mb-2">Languages</h4>
                <p className="text-gray-300 text-sm">English, Urdu, Hindi</p>
              </div>
              <div className="bg-gray-700 p-4 rounded-lg">
                <h4 className="text-purple-400 font-semibold mb-2">Location</h4>
                <p className="text-gray-300 text-sm">Lahore, Pakistan</p>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default About
