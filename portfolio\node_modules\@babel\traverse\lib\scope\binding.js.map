{"version": 3, "names": ["Binding", "constructor", "identifier", "scope", "path", "kind", "constantViolations", "constant", "referencePaths", "referenced", "references", "isInitInLoop", "reassign", "clearValue", "deoptValue", "hasDeoptedValue", "setValue", "value", "hasValue", "includes", "push", "reference", "dereference", "exports", "default", "isFunctionDeclarationOrHasInit", "isVariableDeclarator", "node", "init", "parentPath", "key", "isFunctionParent", "isForXStatement", "isLoop"], "sources": ["../../src/scope/binding.ts"], "sourcesContent": ["import type NodePath from \"../path/index.ts\";\nimport type * as t from \"@babel/types\";\nimport type Scope from \"./index.ts\";\n\nexport type BindingKind =\n  | \"var\" /* var declarator */\n  | \"let\" /* let declarator, class declaration id, catch clause parameters */\n  | \"const\" /* const/using/await using declarator */\n  | \"module\" /* import specifiers */\n  | \"hoisted\" /* function declaration id */\n  | \"param\" /* function declaration parameters */\n  | \"local\" /* function expression id, class expression id */\n  | \"unknown\"; /* export specifiers */\n/**\n * This class is responsible for a binding inside of a scope.\n *\n * It tracks the following:\n *\n *  * Node path.\n *  * Amount of times referenced by other nodes.\n *  * Paths to nodes that reassign or modify this binding.\n *  * The kind of binding. (Is it a parameter, declaration etc)\n */\n\nexport default class Binding {\n  identifier: t.Identifier;\n  scope: Scope;\n  path: NodePath;\n  kind: BindingKind;\n\n  constructor({\n    identifier,\n    scope,\n    path,\n    kind,\n  }: {\n    identifier: t.Identifier;\n    scope: Scope;\n    path: NodePath;\n    kind: BindingKind;\n  }) {\n    this.identifier = identifier;\n    this.scope = scope;\n    this.path = path;\n    this.kind = kind;\n\n    if ((kind === \"var\" || kind === \"hoisted\") && isInitInLoop(path)) {\n      this.reassign(path);\n    }\n\n    this.clearValue();\n  }\n\n  constantViolations: Array<NodePath> = [];\n  constant: boolean = true;\n\n  referencePaths: Array<NodePath> = [];\n  referenced: boolean = false;\n  references: number = 0;\n\n  declare hasDeoptedValue: boolean;\n  declare hasValue: boolean;\n  declare value: any;\n\n  deoptValue() {\n    this.clearValue();\n    this.hasDeoptedValue = true;\n  }\n\n  setValue(value: any) {\n    if (this.hasDeoptedValue) return;\n    this.hasValue = true;\n    this.value = value;\n  }\n\n  clearValue() {\n    this.hasDeoptedValue = false;\n    this.hasValue = false;\n    this.value = null;\n  }\n\n  /**\n   * Register a constant violation with the provided `path`.\n   */\n\n  reassign(path: NodePath) {\n    this.constant = false;\n    if (this.constantViolations.includes(path)) {\n      return;\n    }\n    this.constantViolations.push(path);\n  }\n\n  /**\n   * Increment the amount of references to this binding.\n   */\n\n  reference(path: NodePath) {\n    if (this.referencePaths.includes(path)) {\n      return;\n    }\n    this.referenced = true;\n    this.references++;\n    this.referencePaths.push(path);\n  }\n\n  /**\n   * Decrement the amount of references to this binding.\n   */\n\n  dereference() {\n    this.references--;\n    this.referenced = !!this.references;\n  }\n}\n\nfunction isInitInLoop(path: NodePath) {\n  const isFunctionDeclarationOrHasInit =\n    !path.isVariableDeclarator() || path.node.init;\n  for (\n    let { parentPath, key } = path;\n    parentPath;\n    { parentPath, key } = parentPath\n  ) {\n    if (parentPath.isFunctionParent()) return false;\n    if (\n      (key === \"left\" && parentPath.isForXStatement()) ||\n      (isFunctionDeclarationOrHasInit && key === \"body\" && parentPath.isLoop())\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;AAwBe,MAAMA,OAAO,CAAC;EAM3BC,WAAWA,CAAC;IACVC,UAAU;IACVC,KAAK;IACLC,IAAI;IACJC;EAMF,CAAC,EAAE;IAAA,KAfHH,UAAU;IAAA,KACVC,KAAK;IAAA,KACLC,IAAI;IAAA,KACJC,IAAI;IAAA,KAyBJC,kBAAkB,GAAoB,EAAE;IAAA,KACxCC,QAAQ,GAAY,IAAI;IAAA,KAExBC,cAAc,GAAoB,EAAE;IAAA,KACpCC,UAAU,GAAY,KAAK;IAAA,KAC3BC,UAAU,GAAW,CAAC;IAjBpB,IAAI,CAACR,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,IAAI,GAAGA,IAAI;IAEhB,IAAI,CAACA,IAAI,KAAK,KAAK,IAAIA,IAAI,KAAK,SAAS,KAAKM,YAAY,CAACP,IAAI,CAAC,EAAE;MAChE,IAAI,CAACQ,QAAQ,CAACR,IAAI,CAAC;IACrB;IAEA,IAAI,CAACS,UAAU,CAAC,CAAC;EACnB;EAaAC,UAAUA,CAAA,EAAG;IACX,IAAI,CAACD,UAAU,CAAC,CAAC;IACjB,IAAI,CAACE,eAAe,GAAG,IAAI;EAC7B;EAEAC,QAAQA,CAACC,KAAU,EAAE;IACnB,IAAI,IAAI,CAACF,eAAe,EAAE;IAC1B,IAAI,CAACG,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;EACpB;EAEAJ,UAAUA,CAAA,EAAG;IACX,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACD,KAAK,GAAG,IAAI;EACnB;EAMAL,QAAQA,CAACR,IAAc,EAAE;IACvB,IAAI,CAACG,QAAQ,GAAG,KAAK;IACrB,IAAI,IAAI,CAACD,kBAAkB,CAACa,QAAQ,CAACf,IAAI,CAAC,EAAE;MAC1C;IACF;IACA,IAAI,CAACE,kBAAkB,CAACc,IAAI,CAAChB,IAAI,CAAC;EACpC;EAMAiB,SAASA,CAACjB,IAAc,EAAE;IACxB,IAAI,IAAI,CAACI,cAAc,CAACW,QAAQ,CAACf,IAAI,CAAC,EAAE;MACtC;IACF;IACA,IAAI,CAACK,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,UAAU,EAAE;IACjB,IAAI,CAACF,cAAc,CAACY,IAAI,CAAChB,IAAI,CAAC;EAChC;EAMAkB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACZ,UAAU,EAAE;IACjB,IAAI,CAACD,UAAU,GAAG,CAAC,CAAC,IAAI,CAACC,UAAU;EACrC;AACF;AAACa,OAAA,CAAAC,OAAA,GAAAxB,OAAA;AAED,SAASW,YAAYA,CAACP,IAAc,EAAE;EACpC,MAAMqB,8BAA8B,GAClC,CAACrB,IAAI,CAACsB,oBAAoB,CAAC,CAAC,IAAItB,IAAI,CAACuB,IAAI,CAACC,IAAI;EAChD,KACE,IAAI;IAAEC,UAAU;IAAEC;EAAI,CAAC,GAAG1B,IAAI,EAC9ByB,UAAU,EACV;IAAEA,UAAU;IAAEC;EAAI,CAAC,GAAGD,UAAU,EAChC;IACA,IAAIA,UAAU,CAACE,gBAAgB,CAAC,CAAC,EAAE,OAAO,KAAK;IAC/C,IACGD,GAAG,KAAK,MAAM,IAAID,UAAU,CAACG,eAAe,CAAC,CAAC,IAC9CP,8BAA8B,IAAIK,GAAG,KAAK,MAAM,IAAID,UAAU,CAACI,MAAM,CAAC,CAAE,EACzE;MACA,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}