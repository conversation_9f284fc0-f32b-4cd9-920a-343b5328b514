import React from 'react'
import { motion } from 'framer-motion'

const Certifications = () => {
  const certifications = [
    {
      id: 1,
      title: "Third Place @ Hackforge",
      organization: "NUML University",
      date: "2024",
      description: "Achieved third place in the competitive Hackforge programming competition, demonstrating strong problem-solving and coding skills.",
      icon: "🥉",
      color: "from-yellow-500 to-orange-500"
    },
    {
      id: 2,
      title: "Hackforge Event Host",
      organization: "ACM NUML Chapter",
      date: "2023",
      description: "Successfully organized and hosted the Hackforge competition, managing logistics and coordinating with participants and judges.",
      icon: "🎯",
      color: "from-purple-500 to-pink-500"
    },
    {
      id: 3,
      title: "Chess Tournament Organizer",
      organization: "ACM NUML Chapter",
      date: "2023",
      description: "Organized and managed a university-wide chess tournament, fostering strategic thinking and community engagement among students.",
      icon: "♟️",
      color: "from-blue-500 to-cyan-500"
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  }

  const cardVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  return (
    <section id="certifications" className="py-20 bg-gray-800">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            Achievements & Certifications
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            Recognition and achievements that highlight my commitment to excellence 
            and community involvement in the tech space.
          </motion.p>
        </motion.div>

        <motion.div
          className="grid md:grid-cols-3 gap-8"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          {certifications.map((cert) => (
            <motion.div
              key={cert.id}
              className="bg-gray-900 rounded-lg p-6 shadow-lg hover:shadow-2xl transition-all duration-300"
              variants={cardVariants}
              whileHover={{ y: -5, scale: 1.02 }}
            >
              <div className="text-center mb-6">
                <motion.div
                  className={`w-20 h-20 mx-auto mb-4 rounded-full bg-gradient-to-r ${cert.color} flex items-center justify-center text-3xl`}
                  whileHover={{ rotate: 360 }}
                  transition={{ duration: 0.6 }}
                >
                  {cert.icon}
                </motion.div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {cert.title}
                </h3>
                <p className="text-purple-400 font-medium mb-1">
                  {cert.organization}
                </p>
                <p className="text-gray-500 text-sm">
                  {cert.date}
                </p>
              </div>
              
              <p className="text-gray-300 text-sm leading-relaxed text-center">
                {cert.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={itemVariants}
        >
          <motion.p
            className="text-gray-400 text-lg"
            variants={itemVariants}
          >
            More achievements and certifications coming soon! 🚀
          </motion.p>
        </motion.div>
      </div>
    </section>
  )
}

export default Certifications
