/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        'rich-black': {
          50: '#f6f7f8',
          100: '#e8eaed',
          200: '#d4d8dd',
          300: '#b6bcc5',
          400: '#929ba7',
          500: '#0d1b2a',
          600: '#0b1724',
          700: '#09131e',
          800: '#070f18',
          900: '#050b12',
        },
        'oxford-blue': {
          50: '#f4f6f8',
          100: '#e6eaf0',
          200: '#d0d7e2',
          300: '#aeb9cb',
          400: '#8594af',
          500: '#1b263b',
          600: '#182135',
          700: '#141c2e',
          800: '#101727',
          900: '#0c1220',
        },
        'yinmn-blue': {
          50: '#f6f8fa',
          100: '#eaeff4',
          200: '#d8e1ea',
          300: '#bcc9d8',
          400: '#9bacc2',
          500: '#415a77',
          600: '#3a516b',
          700: '#32475e',
          800: '#2a3d51',
          900: '#223344',
        },
        'silver-lake-blue': {
          50: '#f8f9fb',
          100: '#eff2f6',
          200: '#e2e7ed',
          300: '#cfd7e1',
          400: '#b7c3d2',
          500: '#778da9',
          600: '#6b7f98',
          700: '#5e7087',
          800: '#516176',
          900: '#445265',
        },
        'platinum': {
          50: '#fefefe',
          100: '#fdfdfc',
          200: '#fafaf9',
          300: '#f6f6f5',
          400: '#f0f0ef',
          500: '#e0e1dd',
          600: '#cacacc',
          700: '#b4b4bb',
          800: '#9e9eaa',
          900: '#888899',
        },
      },
      fontFamily: {
        'inter': ['Inter', 'sans-serif'],
      },
      animation: {
        'fadeInUp': 'fadeInUp 0.6s ease-out',
        'slideInLeft': 'slideInLeft 0.6s ease-out',
        'slideInRight': 'slideInRight 0.6s ease-out',
      },
      keyframes: {
        fadeInUp: {
          '0%': {
            opacity: '0',
            transform: 'translateY(30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateY(0)',
          },
        },
        slideInLeft: {
          '0%': {
            opacity: '0',
            transform: 'translateX(-30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
        slideInRight: {
          '0%': {
            opacity: '0',
            transform: 'translateX(30px)',
          },
          '100%': {
            opacity: '1',
            transform: 'translateX(0)',
          },
        },
      },
    },
  },
  plugins: [],
}
