{"version": 3, "names": ["_isValidIdentifier", "require", "RESERVED_WORDS_ES3_ONLY", "Set", "isValidES3Identifier", "name", "isValidIdentifier", "has"], "sources": ["../../src/validators/isValidES3Identifier.ts"], "sourcesContent": ["import isValidIdentifier from \"./isValidIdentifier.ts\";\n\nconst RESERVED_WORDS_ES3_ONLY: Set<string> = new Set([\n  \"abstract\",\n  \"boolean\",\n  \"byte\",\n  \"char\",\n  \"double\",\n  \"enum\",\n  \"final\",\n  \"float\",\n  \"goto\",\n  \"implements\",\n  \"int\",\n  \"interface\",\n  \"long\",\n  \"native\",\n  \"package\",\n  \"private\",\n  \"protected\",\n  \"public\",\n  \"short\",\n  \"static\",\n  \"synchronized\",\n  \"throws\",\n  \"transient\",\n  \"volatile\",\n]);\n\n/**\n * Check if the input `name` is a valid identifier name according to the ES3 specification.\n *\n * Additional ES3 reserved words are\n */\nexport default function isValidES3Identifier(name: string): boolean {\n  return isValidIdentifier(name) && !RESERVED_WORDS_ES3_ONLY.has(name);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAEA,MAAMC,uBAAoC,GAAG,IAAIC,GAAG,CAAC,CACnD,UAAU,EACV,SAAS,EACT,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,OAAO,EACP,OAAO,EACP,MAAM,EACN,YAAY,EACZ,KAAK,EACL,WAAW,EACX,MAAM,EACN,QAAQ,EACR,SAAS,EACT,SAAS,EACT,WAAW,EACX,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,cAAc,EACd,QAAQ,EACR,WAAW,EACX,UAAU,CACX,CAAC;AAOa,SAASC,oBAAoBA,CAACC,IAAY,EAAW;EAClE,OAAO,IAAAC,0BAAiB,EAACD,IAAI,CAAC,IAAI,CAACH,uBAAuB,CAACK,GAAG,CAACF,IAAI,CAAC;AACtE", "ignoreList": []}