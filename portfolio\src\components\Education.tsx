import React from 'react'
import { motion } from 'framer-motion'

const Education = () => {
  const timeline = [
    {
      id: 1,
      type: "education",
      title: "Bachelor of Science in Computer Science",
      institution: "University Name",
      period: "2021 - 2025",
      description: "Focused on software engineering, artificial intelligence, and data structures. Maintained a 3.8 GPA while participating in various coding competitions and hackathons.",
      achievements: ["Dean's List", "Programming Competition Winner", "AI Research Assistant"]
    },
    {
      id: 2,
      type: "experience",
      title: "Software Development Intern",
      institution: "Tech Company Inc.",
      period: "Summer 2024",
      description: "Developed full-stack web applications using React and Node.js. Collaborated with senior developers to implement new features and optimize existing codebase.",
      achievements: ["Improved app performance by 30%", "Led junior intern team", "Implemented CI/CD pipeline"]
    },
    {
      id: 3,
      type: "education",
      title: "High School Diploma",
      institution: "High School Name",
      period: "2017 - 2021",
      description: "Graduated with honors, focusing on mathematics and computer science. Founded the school's first coding club and organized programming workshops.",
      achievements: ["Valedictorian", "Founded Coding Club", "National Merit Scholar"]
    },
    {
      id: 4,
      type: "experience",
      title: "Freelance Web Developer",
      institution: "Self-Employed",
      period: "2020 - Present",
      description: "Created custom websites and web applications for local businesses and startups. Managed projects from conception to deployment.",
      achievements: ["20+ successful projects", "5-star client rating", "Built recurring client base"]
    }
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { x: -50, opacity: 0 },
    visible: {
      x: 0,
      opacity: 1,
      transition: {
        duration: 0.6
      }
    }
  }

  const timelineVariants = {
    hidden: { scaleY: 0 },
    visible: {
      scaleY: 1,
      transition: {
        duration: 1,
        delay: 0.5
      }
    }
  }

  return (
    <section id="education" className="py-20 bg-gray-900">
      <div className="max-w-6xl mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          variants={containerVariants}
        >
          <motion.h2
            className="text-4xl md:text-5xl font-bold text-white mb-4"
            variants={itemVariants}
          >
            Education & Experience
          </motion.h2>
          <motion.div
            className="w-24 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mb-4"
            variants={itemVariants}
          />
          <motion.p
            className="text-gray-400 max-w-2xl mx-auto"
            variants={itemVariants}
          >
            My academic journey and professional experiences that have shaped my skills and perspective.
          </motion.p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <motion.div
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-purple-500 to-pink-500 h-full origin-top"
            variants={timelineVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          />

          <motion.div
            className="space-y-12"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={containerVariants}
          >
            {timeline.map((item, index) => (
              <motion.div
                key={item.id}
                className={`flex items-center ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
                variants={itemVariants}
              >
                <div className={`w-1/2 ${index % 2 === 0 ? 'pr-8' : 'pl-8'}`}>
                  <motion.div
                    className="bg-gray-800 p-6 rounded-lg shadow-lg"
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <div className="flex items-center mb-3">
                      <div className={`w-3 h-3 rounded-full mr-3 ${
                        item.type === 'education' 
                          ? 'bg-purple-500' 
                          : 'bg-pink-500'
                      }`} />
                      <span className="text-gray-400 text-sm font-medium">
                        {item.period}
                      </span>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {item.title}
                    </h3>
                    
                    <h4 className="text-purple-400 font-medium mb-3">
                      {item.institution}
                    </h4>
                    
                    <p className="text-gray-300 mb-4 leading-relaxed">
                      {item.description}
                    </p>
                    
                    <div className="space-y-2">
                      <h5 className="text-sm font-semibold text-gray-400 uppercase tracking-wide">
                        Key Achievements
                      </h5>
                      <ul className="space-y-1">
                        {item.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="text-gray-300 text-sm flex items-center">
                            <span className="w-1.5 h-1.5 bg-purple-400 rounded-full mr-2" />
                            {achievement}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </motion.div>
                </div>

                {/* Timeline dot */}
                <motion.div
                  className="relative z-10"
                  whileHover={{ scale: 1.2 }}
                >
                  <div className={`w-6 h-6 rounded-full border-4 border-gray-900 ${
                    item.type === 'education' 
                      ? 'bg-purple-500' 
                      : 'bg-pink-500'
                  }`} />
                </motion.div>

                <div className="w-1/2" />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default Education
