import React from 'react'
import { motion } from 'framer-motion'
import { ThemeProvider } from './contexts/ThemeContext'
import Navbar from './components/Navbar'
import Hero from './components/Hero'
import About from './components/About'
import Projects from './components/Projects'
import Skills from './components/Skills'
import Education from './components/Education'
import Certifications from './components/Certifications'
import Testimonials from './components/Testimonials'
import GitHubRepos from './components/GitHubRepos'
import Blog from './components/Blog'
import Contact from './components/Contact'
import Footer from './components/Footer'

function App() {
  return (
    <ThemeProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300">
        <Navbar />
        <section id="home">
          <Hero />
        </section>
        <About />
        <Projects />
        <Skills />
        <Education />
        <Certifications />
        <Testimonials />
        <GitHubRepos />
        <Blog />
        <Contact />
        <Footer />
      </div>
    </ThemeProvider>
  )
}

export default App
