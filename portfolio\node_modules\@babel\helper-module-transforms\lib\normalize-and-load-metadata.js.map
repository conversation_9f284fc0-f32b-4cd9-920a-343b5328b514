{"version": 3, "names": ["_path", "require", "_helperValidatorIdentifier", "hasExports", "metadata", "isSideEffectImport", "source", "imports", "size", "importsNamespace", "reexports", "reexportNamespace", "reexportAll", "validateImportInteropOption", "importInterop", "Error", "resolveImportInterop", "filename", "normalizeModuleAndLoadMetadata", "programPath", "exportName", "initializeReexports", "getWrapperPayload", "esNamespaceOnly", "scope", "generateUidIdentifier", "name", "stringSpecifiers", "Set", "nameAnonymousExports", "local", "sources", "getModuleMetadata", "removeImportExportDeclarations", "nameOfnamespace", "resolvedInterop", "interop", "exportNameListName", "getExportSpecifierName", "path", "isIdentifier", "node", "isStringLiteral", "stringValue", "value", "isIdentifierName", "add", "type", "assertExportSpecifier", "isExportSpecifier", "isExportNamespaceSpecifier", "buildCodeFrameError", "localData", "getLocalExportMetadata", "importNodes", "Map", "sourceData", "getData", "sourceNode", "data", "get", "basename", "extname", "loc", "wrap", "lazy", "referenced", "set", "push", "for<PERSON>ach", "child", "isImportDeclaration", "spec", "isImportDefaultSpecifier", "localName", "reexport", "delete", "names", "isImportNamespaceSpecifier", "isImportSpecifier", "importName", "isExportAllDeclaration", "isExportNamedDeclaration", "isExportDefaultDeclaration", "values", "needsDefault", "needsNamed", "bindingKindLookup", "programScope", "programC<PERSON><PERSON>n", "kind", "declaration", "isFunctionDeclaration", "isClassDeclaration", "isVariableDeclaration", "Object", "keys", "getOuterBindingIdentifiers", "localMetadata", "getLocalMetadata", "idPath", "_bindingKindLookup$ge", "_programScope$getBind", "getBinding", "undefined", "ids", "getOuterBindingIdentifierPaths", "exported", "_child$splitExportDec", "splitExportDeclaration", "NodePath", "prototype", "remove", "_blockHoist", "replaceWith"], "sources": ["../src/normalize-and-load-metadata.ts"], "sourcesContent": ["import { basename, extname } from \"node:path\";\nimport type { types as t, NodePath } from \"@babel/core\";\n\nimport { isIdentifierName } from \"@babel/helper-validator-identifier\";\n\nexport interface ModuleMetadata {\n  exportName: string;\n  // The name of the variable that will reference an object containing export names.\n  exportNameListName: null | string;\n  hasExports: boolean;\n  // Lookup from local binding to export information.\n  local: Map<string, LocalExportMetadata>;\n  // Lookup of source file to source file metadata.\n  source: Map<string, SourceModuleMetadata>;\n  // List of names that should only be printed as string literals.\n  // i.e. `import { \"any unicode\" as foo } from \"some-module\"`\n  // `stringSpecifiers` is Set(1) [\"any unicode\"]\n  // In most cases `stringSpecifiers` is an empty Set\n  stringSpecifiers: Set<string>;\n}\n\nexport type InteropType =\n  | \"default\" // Babel interop for default-only imports\n  | \"namespace\" // Babel interop for namespace or default+named imports\n  | \"node-default\" // Node.js interop for default-only imports\n  | \"node-namespace\" // Node.js interop for namespace or default+named imports\n  | \"none\"; // No interop, or named-only imports\n\nexport type ImportInterop =\n  | \"none\"\n  | \"babel\"\n  | \"node\"\n  | ((source: string, filename?: string) => \"none\" | \"babel\" | \"node\");\n\nexport interface SourceModuleMetadata {\n  // A unique variable name to use for this namespace object. Centralized for simplicity.\n  name: string;\n  loc: t.SourceLocation | undefined | null;\n  interop: InteropType;\n  // Local binding to reference from this source namespace. Key: Local name, value: Import name\n  imports: Map<string, string>;\n  // Local names that reference namespace object.\n  importsNamespace: Set<string>;\n  // Reexports to create for namespace. Key: Export name, value: Import name\n  reexports: Map<string, string>;\n  // List of names to re-export namespace as.\n  reexportNamespace: Set<string>;\n  // Tracks if the source should be re-exported.\n  reexportAll: null | {\n    loc: t.SourceLocation | undefined | null;\n  };\n  wrap?: unknown;\n  referenced: boolean;\n}\n\nexport interface LocalExportMetadata {\n  names: Array<string>; // names of exports,\n  kind: \"import\" | \"hoisted\" | \"block\" | \"var\";\n}\n\n/**\n * Check if the module has any exports that need handling.\n */\nexport function hasExports(metadata: ModuleMetadata) {\n  return metadata.hasExports;\n}\n\n/**\n * Check if a given source is an anonymous import, e.g. \"import 'foo';\"\n */\nexport function isSideEffectImport(source: SourceModuleMetadata) {\n  return (\n    source.imports.size === 0 &&\n    source.importsNamespace.size === 0 &&\n    source.reexports.size === 0 &&\n    source.reexportNamespace.size === 0 &&\n    !source.reexportAll\n  );\n}\n\nexport function validateImportInteropOption(\n  importInterop: any,\n): importInterop is ImportInterop {\n  if (\n    typeof importInterop !== \"function\" &&\n    importInterop !== \"none\" &&\n    importInterop !== \"babel\" &&\n    importInterop !== \"node\"\n  ) {\n    throw new Error(\n      `.importInterop must be one of \"none\", \"babel\", \"node\", or a function returning one of those values (received ${importInterop}).`,\n    );\n  }\n  return importInterop;\n}\n\nfunction resolveImportInterop(\n  importInterop: ImportInterop,\n  source: string,\n  filename: string | undefined,\n) {\n  if (typeof importInterop === \"function\") {\n    return validateImportInteropOption(importInterop(source, filename));\n  }\n  return importInterop;\n}\n\n/**\n * Remove all imports and exports from the file, and return all metadata\n * needed to reconstruct the module's behavior.\n */\nexport default function normalizeModuleAndLoadMetadata(\n  programPath: NodePath<t.Program>,\n  exportName: string,\n  {\n    importInterop,\n    initializeReexports = false,\n    getWrapperPayload,\n    esNamespaceOnly = false,\n    filename,\n  }: {\n    importInterop: ImportInterop;\n    initializeReexports: boolean | void;\n    getWrapperPayload?: (\n      source: string,\n      metadata: SourceModuleMetadata,\n      importNodes: t.Node[],\n    ) => unknown;\n    esNamespaceOnly: boolean;\n    filename: string;\n  },\n): ModuleMetadata {\n  if (!exportName) {\n    exportName = programPath.scope.generateUidIdentifier(\"exports\").name;\n  }\n  const stringSpecifiers = new Set<string>();\n\n  nameAnonymousExports(programPath);\n\n  const { local, sources, hasExports } = getModuleMetadata(\n    programPath,\n    { initializeReexports, getWrapperPayload },\n    stringSpecifiers,\n  );\n\n  removeImportExportDeclarations(programPath);\n\n  // Reuse the imported namespace name if there is one.\n  for (const [source, metadata] of sources) {\n    const { importsNamespace, imports } = metadata;\n    // If there is at least one namespace import and other imports, it may collipse with local ident, can be seen in issue 15879.\n    if (importsNamespace.size > 0 && imports.size === 0) {\n      const [nameOfnamespace] = importsNamespace;\n      metadata.name = nameOfnamespace;\n    }\n\n    const resolvedInterop = resolveImportInterop(\n      importInterop,\n      source,\n      filename,\n    );\n\n    if (resolvedInterop === \"none\") {\n      metadata.interop = \"none\";\n    } else if (resolvedInterop === \"node\" && metadata.interop === \"namespace\") {\n      metadata.interop = \"node-namespace\";\n    } else if (resolvedInterop === \"node\" && metadata.interop === \"default\") {\n      metadata.interop = \"node-default\";\n    } else if (esNamespaceOnly && metadata.interop === \"namespace\") {\n      // Both the default and namespace interops pass through __esModule\n      // objects, but the namespace interop is used to enable Babel's\n      // destructuring-like interop behavior for normal CommonJS.\n      // Since some tooling has started to remove that behavior, we expose\n      // it as the `esNamespace` option.\n      metadata.interop = \"default\";\n    }\n  }\n\n  return {\n    exportName,\n    exportNameListName: null,\n    hasExports,\n    local,\n    source: sources,\n    stringSpecifiers,\n  };\n}\n\nfunction getExportSpecifierName(\n  path: NodePath,\n  stringSpecifiers: Set<string>,\n): string {\n  if (path.isIdentifier()) {\n    return path.node.name;\n  } else if (path.isStringLiteral()) {\n    const stringValue = path.node.value;\n    // add specifier value to `stringSpecifiers` only when it can not be converted to an identifier name\n    // i.e In `import { \"foo\" as bar }`\n    // we do not consider `\"foo\"` to be a `stringSpecifier` because we can treat it as\n    // `import { foo as bar }`\n    // This helps minimize the size of `stringSpecifiers` and reduce overhead of checking valid identifier names\n    // when building transpiled code from metadata\n    if (!isIdentifierName(stringValue)) {\n      stringSpecifiers.add(stringValue);\n    }\n    return stringValue;\n  } else {\n    throw new Error(\n      `Expected export specifier to be either Identifier or StringLiteral, got ${path.node.type}`,\n    );\n  }\n}\n\nfunction assertExportSpecifier(\n  path: NodePath,\n): asserts path is NodePath<t.ExportSpecifier> {\n  if (path.isExportSpecifier()) {\n    return;\n  } else if (path.isExportNamespaceSpecifier()) {\n    throw path.buildCodeFrameError(\n      \"Export namespace should be first transformed by `@babel/plugin-transform-export-namespace-from`.\",\n    );\n  } else {\n    throw path.buildCodeFrameError(\"Unexpected export specifier type\");\n  }\n}\n\n/**\n * Get metadata about the imports and exports present in this module.\n */\nfunction getModuleMetadata(\n  programPath: NodePath<t.Program>,\n  {\n    getWrapperPayload,\n    initializeReexports,\n  }: {\n    getWrapperPayload?: (\n      source: string,\n      metadata: SourceModuleMetadata,\n      importNodes: t.Node[],\n    ) => unknown;\n    initializeReexports: boolean | void;\n  },\n  stringSpecifiers: Set<string>,\n) {\n  const localData = getLocalExportMetadata(\n    programPath,\n    initializeReexports,\n    stringSpecifiers,\n  );\n\n  const importNodes = new Map<string, t.Node[]>();\n  const sourceData = new Map<string, SourceModuleMetadata>();\n  const getData = (sourceNode: t.StringLiteral, node: t.Node) => {\n    const source = sourceNode.value;\n\n    let data = sourceData.get(source);\n    if (!data) {\n      data = {\n        name: programPath.scope.generateUidIdentifier(\n          basename(source, extname(source)),\n        ).name,\n\n        interop: \"none\",\n\n        loc: null,\n\n        // Data about the requested sources and names.\n        imports: new Map(),\n        importsNamespace: new Set(),\n\n        // Metadata about data that is passed directly from source to export.\n        reexports: new Map(),\n        reexportNamespace: new Set(),\n        reexportAll: null,\n\n        wrap: null,\n\n        // @ts-expect-error lazy is not listed in the type.\n        // This is needed for compatibility with older version of the commonjs\n        // plusing.\n        // TODO(Babel 8): Remove this\n        get lazy() {\n          return this.wrap === \"lazy\";\n        },\n\n        referenced: false,\n      };\n      sourceData.set(source, data);\n      importNodes.set(source, [node]);\n    } else {\n      importNodes.get(source).push(node);\n    }\n    return data;\n  };\n  let hasExports = false;\n  programPath.get(\"body\").forEach(child => {\n    if (child.isImportDeclaration()) {\n      const data = getData(child.node.source, child.node);\n      if (!data.loc) data.loc = child.node.loc;\n\n      child.get(\"specifiers\").forEach(spec => {\n        if (spec.isImportDefaultSpecifier()) {\n          const localName = spec.get(\"local\").node.name;\n\n          data.imports.set(localName, \"default\");\n\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexports.set(name, \"default\");\n            });\n            data.referenced = true;\n          }\n        } else if (spec.isImportNamespaceSpecifier()) {\n          const localName = spec.get(\"local\").node.name;\n\n          data.importsNamespace.add(localName);\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexportNamespace.add(name);\n            });\n            data.referenced = true;\n          }\n        } else if (spec.isImportSpecifier()) {\n          const importName = getExportSpecifierName(\n            spec.get(\"imported\"),\n            stringSpecifiers,\n          );\n          const localName = spec.get(\"local\").node.name;\n\n          data.imports.set(localName, importName);\n\n          const reexport = localData.get(localName);\n          if (reexport) {\n            localData.delete(localName);\n\n            reexport.names.forEach(name => {\n              data.reexports.set(name, importName);\n            });\n            data.referenced = true;\n          }\n        }\n      });\n    } else if (child.isExportAllDeclaration()) {\n      hasExports = true;\n      const data = getData(child.node.source, child.node);\n      if (!data.loc) data.loc = child.node.loc;\n\n      data.reexportAll = {\n        loc: child.node.loc,\n      };\n      data.referenced = true;\n    } else if (child.isExportNamedDeclaration() && child.node.source) {\n      hasExports = true;\n      const data = getData(child.node.source, child.node);\n      if (!data.loc) data.loc = child.node.loc;\n\n      child.get(\"specifiers\").forEach(spec => {\n        assertExportSpecifier(spec);\n        const importName = getExportSpecifierName(\n          spec.get(\"local\"),\n          stringSpecifiers,\n        );\n        const exportName = getExportSpecifierName(\n          spec.get(\"exported\"),\n          stringSpecifiers,\n        );\n\n        data.reexports.set(exportName, importName);\n        data.referenced = true;\n\n        if (exportName === \"__esModule\") {\n          throw spec\n            .get(\"exported\")\n            .buildCodeFrameError('Illegal export \"__esModule\".');\n        }\n      });\n    } else if (\n      child.isExportNamedDeclaration() ||\n      child.isExportDefaultDeclaration()\n    ) {\n      hasExports = true;\n    }\n  });\n\n  for (const metadata of sourceData.values()) {\n    let needsDefault = false;\n    let needsNamed = false;\n\n    if (metadata.importsNamespace.size > 0) {\n      needsDefault = true;\n      needsNamed = true;\n    }\n\n    if (metadata.reexportAll) {\n      needsNamed = true;\n    }\n\n    for (const importName of metadata.imports.values()) {\n      if (importName === \"default\") needsDefault = true;\n      else needsNamed = true;\n    }\n    for (const importName of metadata.reexports.values()) {\n      if (importName === \"default\") needsDefault = true;\n      else needsNamed = true;\n    }\n\n    if (needsDefault && needsNamed) {\n      // TODO(logan): Using the namespace interop here is unfortunate. Revisit.\n      metadata.interop = \"namespace\";\n    } else if (needsDefault) {\n      metadata.interop = \"default\";\n    }\n  }\n\n  if (getWrapperPayload) {\n    for (const [source, metadata] of sourceData) {\n      metadata.wrap = getWrapperPayload(\n        source,\n        metadata,\n        importNodes.get(source),\n      );\n    }\n  }\n\n  return {\n    hasExports,\n    local: localData,\n    sources: sourceData,\n  };\n}\n\ntype ModuleBindingKind = \"import\" | \"hoisted\" | \"block\" | \"var\";\n/**\n * Get metadata about local variables that are exported.\n */\nfunction getLocalExportMetadata(\n  programPath: NodePath<t.Program>,\n  initializeReexports: boolean | void,\n  stringSpecifiers: Set<string>,\n): Map<string, LocalExportMetadata> {\n  const bindingKindLookup = new Map();\n\n  const programScope = programPath.scope;\n  const programChildren = programPath.get(\"body\");\n\n  programChildren.forEach((child: NodePath) => {\n    let kind: ModuleBindingKind;\n    if (child.isImportDeclaration()) {\n      kind = \"import\";\n    } else {\n      if (child.isExportDefaultDeclaration()) {\n        child = child.get(\"declaration\");\n      }\n      if (child.isExportNamedDeclaration()) {\n        if (child.node.declaration) {\n          child = child.get(\"declaration\");\n        } else if (\n          initializeReexports &&\n          child.node.source &&\n          child.get(\"source\").isStringLiteral()\n        ) {\n          child.get(\"specifiers\").forEach(spec => {\n            assertExportSpecifier(spec);\n            bindingKindLookup.set(spec.get(\"local\").node.name, \"block\");\n          });\n          return;\n        }\n      }\n\n      if (child.isFunctionDeclaration()) {\n        kind = \"hoisted\";\n      } else if (child.isClassDeclaration()) {\n        kind = \"block\";\n      } else if (child.isVariableDeclaration({ kind: \"var\" })) {\n        kind = \"var\";\n      } else if (child.isVariableDeclaration()) {\n        kind = \"block\";\n      } else {\n        return;\n      }\n    }\n\n    Object.keys(child.getOuterBindingIdentifiers()).forEach(name => {\n      bindingKindLookup.set(name, kind);\n    });\n  });\n\n  const localMetadata = new Map();\n  const getLocalMetadata = (idPath: NodePath<t.Identifier>) => {\n    const localName = idPath.node.name;\n    let metadata = localMetadata.get(localName);\n\n    if (!metadata) {\n      // If localName is not found in the bindingKindLookup generated\n      // from top level declarations, it could be a reference to a var\n      // declaration defined within block statement or switch case\n      const kind: ModuleBindingKind =\n        bindingKindLookup.get(localName) ??\n        (programScope.getBinding(localName)?.kind as \"var\" | undefined);\n\n      if (kind === undefined) {\n        throw idPath.buildCodeFrameError(\n          `Exporting local \"${localName}\", which is not declared.`,\n        );\n      }\n\n      metadata = {\n        names: [],\n        kind,\n      };\n      localMetadata.set(localName, metadata);\n    }\n    return metadata;\n  };\n\n  programChildren.forEach(child => {\n    if (\n      child.isExportNamedDeclaration() &&\n      (initializeReexports || !child.node.source)\n    ) {\n      if (child.node.declaration) {\n        const declaration = child.get(\"declaration\");\n        const ids = declaration.getOuterBindingIdentifierPaths();\n        Object.keys(ids).forEach(name => {\n          if (name === \"__esModule\") {\n            throw declaration.buildCodeFrameError(\n              'Illegal export \"__esModule\".',\n            );\n          }\n          getLocalMetadata(ids[name]).names.push(name);\n        });\n      } else {\n        child.get(\"specifiers\").forEach(spec => {\n          const local = spec.get(\"local\");\n          const exported = spec.get(\"exported\");\n          const localMetadata = getLocalMetadata(local);\n          const exportName = getExportSpecifierName(exported, stringSpecifiers);\n\n          if (exportName === \"__esModule\") {\n            throw exported.buildCodeFrameError('Illegal export \"__esModule\".');\n          }\n          localMetadata.names.push(exportName);\n        });\n      }\n    } else if (child.isExportDefaultDeclaration()) {\n      const declaration = child.get(\"declaration\");\n      if (\n        declaration.isFunctionDeclaration() ||\n        declaration.isClassDeclaration()\n      ) {\n        getLocalMetadata(declaration.get(\"id\")).names.push(\"default\");\n      } else {\n        // These should have been removed by the nameAnonymousExports() call.\n        throw declaration.buildCodeFrameError(\n          \"Unexpected default expression export.\",\n        );\n      }\n    }\n  });\n  return localMetadata;\n}\n\n/**\n * Ensure that all exported values have local binding names.\n */\nfunction nameAnonymousExports(programPath: NodePath<t.Program>) {\n  // Name anonymous exported locals.\n  programPath.get(\"body\").forEach(child => {\n    if (!child.isExportDefaultDeclaration()) return;\n    if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n      // polyfill when being run by an older Babel version\n      child.splitExportDeclaration ??=\n        // eslint-disable-next-line no-restricted-globals\n        require(\"@babel/traverse\").NodePath.prototype.splitExportDeclaration;\n    }\n    child.splitExportDeclaration();\n  });\n}\n\nfunction removeImportExportDeclarations(programPath: NodePath<t.Program>) {\n  programPath.get(\"body\").forEach(child => {\n    if (child.isImportDeclaration()) {\n      child.remove();\n    } else if (child.isExportNamedDeclaration()) {\n      if (child.node.declaration) {\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        child.node.declaration._blockHoist = child.node._blockHoist;\n        child.replaceWith(child.node.declaration);\n      } else {\n        child.remove();\n      }\n    } else if (child.isExportDefaultDeclaration()) {\n      // export default foo;\n      const declaration = child.get(\"declaration\");\n      if (\n        declaration.isFunctionDeclaration() ||\n        declaration.isClassDeclaration()\n      ) {\n        // @ts-expect-error todo(flow->ts): avoid mutations\n        declaration._blockHoist = child.node._blockHoist;\n        child.replaceWith(declaration);\n      } else {\n        // These should have been removed by the nameAnonymousExports() call.\n        throw declaration.buildCodeFrameError(\n          \"Unexpected default expression export.\",\n        );\n      }\n    } else if (child.isExportAllDeclaration()) {\n      child.remove();\n    }\n  });\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAGA,IAAAC,0BAAA,GAAAD,OAAA;AA4DO,SAASE,UAAUA,CAACC,QAAwB,EAAE;EACnD,OAAOA,QAAQ,CAACD,UAAU;AAC5B;AAKO,SAASE,kBAAkBA,CAACC,MAA4B,EAAE;EAC/D,OACEA,MAAM,CAACC,OAAO,CAACC,IAAI,KAAK,CAAC,IACzBF,MAAM,CAACG,gBAAgB,CAACD,IAAI,KAAK,CAAC,IAClCF,MAAM,CAACI,SAAS,CAACF,IAAI,KAAK,CAAC,IAC3BF,MAAM,CAACK,iBAAiB,CAACH,IAAI,KAAK,CAAC,IACnC,CAACF,MAAM,CAACM,WAAW;AAEvB;AAEO,SAASC,2BAA2BA,CACzCC,aAAkB,EACc;EAChC,IACE,OAAOA,aAAa,KAAK,UAAU,IACnCA,aAAa,KAAK,MAAM,IACxBA,aAAa,KAAK,OAAO,IACzBA,aAAa,KAAK,MAAM,EACxB;IACA,MAAM,IAAIC,KAAK,CACb,gHAAgHD,aAAa,IAC/H,CAAC;EACH;EACA,OAAOA,aAAa;AACtB;AAEA,SAASE,oBAAoBA,CAC3BF,aAA4B,EAC5BR,MAAc,EACdW,QAA4B,EAC5B;EACA,IAAI,OAAOH,aAAa,KAAK,UAAU,EAAE;IACvC,OAAOD,2BAA2B,CAACC,aAAa,CAACR,MAAM,EAAEW,QAAQ,CAAC,CAAC;EACrE;EACA,OAAOH,aAAa;AACtB;AAMe,SAASI,8BAA8BA,CACpDC,WAAgC,EAChCC,UAAkB,EAClB;EACEN,aAAa;EACbO,mBAAmB,GAAG,KAAK;EAC3BC,iBAAiB;EACjBC,eAAe,GAAG,KAAK;EACvBN;AAWF,CAAC,EACe;EAChB,IAAI,CAACG,UAAU,EAAE;IACfA,UAAU,GAAGD,WAAW,CAACK,KAAK,CAACC,qBAAqB,CAAC,SAAS,CAAC,CAACC,IAAI;EACtE;EACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAS,CAAC;EAE1CC,oBAAoB,CAACV,WAAW,CAAC;EAEjC,MAAM;IAAEW,KAAK;IAAEC,OAAO;IAAE5B;EAAW,CAAC,GAAG6B,iBAAiB,CACtDb,WAAW,EACX;IAAEE,mBAAmB;IAAEC;EAAkB,CAAC,EAC1CK,gBACF,CAAC;EAEDM,8BAA8B,CAACd,WAAW,CAAC;EAG3C,KAAK,MAAM,CAACb,MAAM,EAAEF,QAAQ,CAAC,IAAI2B,OAAO,EAAE;IACxC,MAAM;MAAEtB,gBAAgB;MAAEF;IAAQ,CAAC,GAAGH,QAAQ;IAE9C,IAAIK,gBAAgB,CAACD,IAAI,GAAG,CAAC,IAAID,OAAO,CAACC,IAAI,KAAK,CAAC,EAAE;MACnD,MAAM,CAAC0B,eAAe,CAAC,GAAGzB,gBAAgB;MAC1CL,QAAQ,CAACsB,IAAI,GAAGQ,eAAe;IACjC;IAEA,MAAMC,eAAe,GAAGnB,oBAAoB,CAC1CF,aAAa,EACbR,MAAM,EACNW,QACF,CAAC;IAED,IAAIkB,eAAe,KAAK,MAAM,EAAE;MAC9B/B,QAAQ,CAACgC,OAAO,GAAG,MAAM;IAC3B,CAAC,MAAM,IAAID,eAAe,KAAK,MAAM,IAAI/B,QAAQ,CAACgC,OAAO,KAAK,WAAW,EAAE;MACzEhC,QAAQ,CAACgC,OAAO,GAAG,gBAAgB;IACrC,CAAC,MAAM,IAAID,eAAe,KAAK,MAAM,IAAI/B,QAAQ,CAACgC,OAAO,KAAK,SAAS,EAAE;MACvEhC,QAAQ,CAACgC,OAAO,GAAG,cAAc;IACnC,CAAC,MAAM,IAAIb,eAAe,IAAInB,QAAQ,CAACgC,OAAO,KAAK,WAAW,EAAE;MAM9DhC,QAAQ,CAACgC,OAAO,GAAG,SAAS;IAC9B;EACF;EAEA,OAAO;IACLhB,UAAU;IACViB,kBAAkB,EAAE,IAAI;IACxBlC,UAAU;IACV2B,KAAK;IACLxB,MAAM,EAAEyB,OAAO;IACfJ;EACF,CAAC;AACH;AAEA,SAASW,sBAAsBA,CAC7BC,IAAc,EACdZ,gBAA6B,EACrB;EACR,IAAIY,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;IACvB,OAAOD,IAAI,CAACE,IAAI,CAACf,IAAI;EACvB,CAAC,MAAM,IAAIa,IAAI,CAACG,eAAe,CAAC,CAAC,EAAE;IACjC,MAAMC,WAAW,GAAGJ,IAAI,CAACE,IAAI,CAACG,KAAK;IAOnC,IAAI,CAAC,IAAAC,2CAAgB,EAACF,WAAW,CAAC,EAAE;MAClChB,gBAAgB,CAACmB,GAAG,CAACH,WAAW,CAAC;IACnC;IACA,OAAOA,WAAW;EACpB,CAAC,MAAM;IACL,MAAM,IAAI5B,KAAK,CACb,2EAA2EwB,IAAI,CAACE,IAAI,CAACM,IAAI,EAC3F,CAAC;EACH;AACF;AAEA,SAASC,qBAAqBA,CAC5BT,IAAc,EAC+B;EAC7C,IAAIA,IAAI,CAACU,iBAAiB,CAAC,CAAC,EAAE;IAC5B;EACF,CAAC,MAAM,IAAIV,IAAI,CAACW,0BAA0B,CAAC,CAAC,EAAE;IAC5C,MAAMX,IAAI,CAACY,mBAAmB,CAC5B,kGACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMZ,IAAI,CAACY,mBAAmB,CAAC,kCAAkC,CAAC;EACpE;AACF;AAKA,SAASnB,iBAAiBA,CACxBb,WAAgC,EAChC;EACEG,iBAAiB;EACjBD;AAQF,CAAC,EACDM,gBAA6B,EAC7B;EACA,MAAMyB,SAAS,GAAGC,sBAAsB,CACtClC,WAAW,EACXE,mBAAmB,EACnBM,gBACF,CAAC;EAED,MAAM2B,WAAW,GAAG,IAAIC,GAAG,CAAmB,CAAC;EAC/C,MAAMC,UAAU,GAAG,IAAID,GAAG,CAA+B,CAAC;EAC1D,MAAME,OAAO,GAAGA,CAACC,UAA2B,EAAEjB,IAAY,KAAK;IAC7D,MAAMnC,MAAM,GAAGoD,UAAU,CAACd,KAAK;IAE/B,IAAIe,IAAI,GAAGH,UAAU,CAACI,GAAG,CAACtD,MAAM,CAAC;IACjC,IAAI,CAACqD,IAAI,EAAE;MACTA,IAAI,GAAG;QACLjC,IAAI,EAAEP,WAAW,CAACK,KAAK,CAACC,qBAAqB,CAC3C,IAAAoC,cAAQ,EAACvD,MAAM,EAAE,IAAAwD,aAAO,EAACxD,MAAM,CAAC,CAClC,CAAC,CAACoB,IAAI;QAENU,OAAO,EAAE,MAAM;QAEf2B,GAAG,EAAE,IAAI;QAGTxD,OAAO,EAAE,IAAIgD,GAAG,CAAC,CAAC;QAClB9C,gBAAgB,EAAE,IAAImB,GAAG,CAAC,CAAC;QAG3BlB,SAAS,EAAE,IAAI6C,GAAG,CAAC,CAAC;QACpB5C,iBAAiB,EAAE,IAAIiB,GAAG,CAAC,CAAC;QAC5BhB,WAAW,EAAE,IAAI;QAEjBoD,IAAI,EAAE,IAAI;QAMV,IAAIC,IAAIA,CAAA,EAAG;UACT,OAAO,IAAI,CAACD,IAAI,KAAK,MAAM;QAC7B,CAAC;QAEDE,UAAU,EAAE;MACd,CAAC;MACDV,UAAU,CAACW,GAAG,CAAC7D,MAAM,EAAEqD,IAAI,CAAC;MAC5BL,WAAW,CAACa,GAAG,CAAC7D,MAAM,EAAE,CAACmC,IAAI,CAAC,CAAC;IACjC,CAAC,MAAM;MACLa,WAAW,CAACM,GAAG,CAACtD,MAAM,CAAC,CAAC8D,IAAI,CAAC3B,IAAI,CAAC;IACpC;IACA,OAAOkB,IAAI;EACb,CAAC;EACD,IAAIxD,UAAU,GAAG,KAAK;EACtBgB,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACS,OAAO,CAACC,KAAK,IAAI;IACvC,IAAIA,KAAK,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAC/B,MAAMZ,IAAI,GAAGF,OAAO,CAACa,KAAK,CAAC7B,IAAI,CAACnC,MAAM,EAAEgE,KAAK,CAAC7B,IAAI,CAAC;MACnD,IAAI,CAACkB,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGO,KAAK,CAAC7B,IAAI,CAACsB,GAAG;MAExCO,KAAK,CAACV,GAAG,CAAC,YAAY,CAAC,CAACS,OAAO,CAACG,IAAI,IAAI;QACtC,IAAIA,IAAI,CAACC,wBAAwB,CAAC,CAAC,EAAE;UACnC,MAAMC,SAAS,GAAGF,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC,CAACnB,IAAI,CAACf,IAAI;UAE7CiC,IAAI,CAACpD,OAAO,CAAC4D,GAAG,CAACO,SAAS,EAAE,SAAS,CAAC;UAEtC,MAAMC,QAAQ,GAAGvB,SAAS,CAACQ,GAAG,CAACc,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZvB,SAAS,CAACwB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAAC3C,IAAI,IAAI;cAC7BiC,IAAI,CAACjD,SAAS,CAACyD,GAAG,CAACzC,IAAI,EAAE,SAAS,CAAC;YACrC,CAAC,CAAC;YACFiC,IAAI,CAACO,UAAU,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIM,IAAI,CAACM,0BAA0B,CAAC,CAAC,EAAE;UAC5C,MAAMJ,SAAS,GAAGF,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC,CAACnB,IAAI,CAACf,IAAI;UAE7CiC,IAAI,CAAClD,gBAAgB,CAACqC,GAAG,CAAC4B,SAAS,CAAC;UACpC,MAAMC,QAAQ,GAAGvB,SAAS,CAACQ,GAAG,CAACc,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZvB,SAAS,CAACwB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAAC3C,IAAI,IAAI;cAC7BiC,IAAI,CAAChD,iBAAiB,CAACmC,GAAG,CAACpB,IAAI,CAAC;YAClC,CAAC,CAAC;YACFiC,IAAI,CAACO,UAAU,GAAG,IAAI;UACxB;QACF,CAAC,MAAM,IAAIM,IAAI,CAACO,iBAAiB,CAAC,CAAC,EAAE;UACnC,MAAMC,UAAU,GAAG1C,sBAAsB,CACvCkC,IAAI,CAACZ,GAAG,CAAC,UAAU,CAAC,EACpBjC,gBACF,CAAC;UACD,MAAM+C,SAAS,GAAGF,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC,CAACnB,IAAI,CAACf,IAAI;UAE7CiC,IAAI,CAACpD,OAAO,CAAC4D,GAAG,CAACO,SAAS,EAAEM,UAAU,CAAC;UAEvC,MAAML,QAAQ,GAAGvB,SAAS,CAACQ,GAAG,CAACc,SAAS,CAAC;UACzC,IAAIC,QAAQ,EAAE;YACZvB,SAAS,CAACwB,MAAM,CAACF,SAAS,CAAC;YAE3BC,QAAQ,CAACE,KAAK,CAACR,OAAO,CAAC3C,IAAI,IAAI;cAC7BiC,IAAI,CAACjD,SAAS,CAACyD,GAAG,CAACzC,IAAI,EAAEsD,UAAU,CAAC;YACtC,CAAC,CAAC;YACFrB,IAAI,CAACO,UAAU,GAAG,IAAI;UACxB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAII,KAAK,CAACW,sBAAsB,CAAC,CAAC,EAAE;MACzC9E,UAAU,GAAG,IAAI;MACjB,MAAMwD,IAAI,GAAGF,OAAO,CAACa,KAAK,CAAC7B,IAAI,CAACnC,MAAM,EAAEgE,KAAK,CAAC7B,IAAI,CAAC;MACnD,IAAI,CAACkB,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGO,KAAK,CAAC7B,IAAI,CAACsB,GAAG;MAExCJ,IAAI,CAAC/C,WAAW,GAAG;QACjBmD,GAAG,EAAEO,KAAK,CAAC7B,IAAI,CAACsB;MAClB,CAAC;MACDJ,IAAI,CAACO,UAAU,GAAG,IAAI;IACxB,CAAC,MAAM,IAAII,KAAK,CAACY,wBAAwB,CAAC,CAAC,IAAIZ,KAAK,CAAC7B,IAAI,CAACnC,MAAM,EAAE;MAChEH,UAAU,GAAG,IAAI;MACjB,MAAMwD,IAAI,GAAGF,OAAO,CAACa,KAAK,CAAC7B,IAAI,CAACnC,MAAM,EAAEgE,KAAK,CAAC7B,IAAI,CAAC;MACnD,IAAI,CAACkB,IAAI,CAACI,GAAG,EAAEJ,IAAI,CAACI,GAAG,GAAGO,KAAK,CAAC7B,IAAI,CAACsB,GAAG;MAExCO,KAAK,CAACV,GAAG,CAAC,YAAY,CAAC,CAACS,OAAO,CAACG,IAAI,IAAI;QACtCxB,qBAAqB,CAACwB,IAAI,CAAC;QAC3B,MAAMQ,UAAU,GAAG1C,sBAAsB,CACvCkC,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC,EACjBjC,gBACF,CAAC;QACD,MAAMP,UAAU,GAAGkB,sBAAsB,CACvCkC,IAAI,CAACZ,GAAG,CAAC,UAAU,CAAC,EACpBjC,gBACF,CAAC;QAEDgC,IAAI,CAACjD,SAAS,CAACyD,GAAG,CAAC/C,UAAU,EAAE4D,UAAU,CAAC;QAC1CrB,IAAI,CAACO,UAAU,GAAG,IAAI;QAEtB,IAAI9C,UAAU,KAAK,YAAY,EAAE;UAC/B,MAAMoD,IAAI,CACPZ,GAAG,CAAC,UAAU,CAAC,CACfT,mBAAmB,CAAC,8BAA8B,CAAC;QACxD;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IACLmB,KAAK,CAACY,wBAAwB,CAAC,CAAC,IAChCZ,KAAK,CAACa,0BAA0B,CAAC,CAAC,EAClC;MACAhF,UAAU,GAAG,IAAI;IACnB;EACF,CAAC,CAAC;EAEF,KAAK,MAAMC,QAAQ,IAAIoD,UAAU,CAAC4B,MAAM,CAAC,CAAC,EAAE;IAC1C,IAAIC,YAAY,GAAG,KAAK;IACxB,IAAIC,UAAU,GAAG,KAAK;IAEtB,IAAIlF,QAAQ,CAACK,gBAAgB,CAACD,IAAI,GAAG,CAAC,EAAE;MACtC6E,YAAY,GAAG,IAAI;MACnBC,UAAU,GAAG,IAAI;IACnB;IAEA,IAAIlF,QAAQ,CAACQ,WAAW,EAAE;MACxB0E,UAAU,GAAG,IAAI;IACnB;IAEA,KAAK,MAAMN,UAAU,IAAI5E,QAAQ,CAACG,OAAO,CAAC6E,MAAM,CAAC,CAAC,EAAE;MAClD,IAAIJ,UAAU,KAAK,SAAS,EAAEK,YAAY,GAAG,IAAI,CAAC,KAC7CC,UAAU,GAAG,IAAI;IACxB;IACA,KAAK,MAAMN,UAAU,IAAI5E,QAAQ,CAACM,SAAS,CAAC0E,MAAM,CAAC,CAAC,EAAE;MACpD,IAAIJ,UAAU,KAAK,SAAS,EAAEK,YAAY,GAAG,IAAI,CAAC,KAC7CC,UAAU,GAAG,IAAI;IACxB;IAEA,IAAID,YAAY,IAAIC,UAAU,EAAE;MAE9BlF,QAAQ,CAACgC,OAAO,GAAG,WAAW;IAChC,CAAC,MAAM,IAAIiD,YAAY,EAAE;MACvBjF,QAAQ,CAACgC,OAAO,GAAG,SAAS;IAC9B;EACF;EAEA,IAAId,iBAAiB,EAAE;IACrB,KAAK,MAAM,CAAChB,MAAM,EAAEF,QAAQ,CAAC,IAAIoD,UAAU,EAAE;MAC3CpD,QAAQ,CAAC4D,IAAI,GAAG1C,iBAAiB,CAC/BhB,MAAM,EACNF,QAAQ,EACRkD,WAAW,CAACM,GAAG,CAACtD,MAAM,CACxB,CAAC;IACH;EACF;EAEA,OAAO;IACLH,UAAU;IACV2B,KAAK,EAAEsB,SAAS;IAChBrB,OAAO,EAAEyB;EACX,CAAC;AACH;AAMA,SAASH,sBAAsBA,CAC7BlC,WAAgC,EAChCE,mBAAmC,EACnCM,gBAA6B,EACK;EAClC,MAAM4D,iBAAiB,GAAG,IAAIhC,GAAG,CAAC,CAAC;EAEnC,MAAMiC,YAAY,GAAGrE,WAAW,CAACK,KAAK;EACtC,MAAMiE,eAAe,GAAGtE,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC;EAE/C6B,eAAe,CAACpB,OAAO,CAAEC,KAAe,IAAK;IAC3C,IAAIoB,IAAuB;IAC3B,IAAIpB,KAAK,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAC/BmB,IAAI,GAAG,QAAQ;IACjB,CAAC,MAAM;MACL,IAAIpB,KAAK,CAACa,0BAA0B,CAAC,CAAC,EAAE;QACtCb,KAAK,GAAGA,KAAK,CAACV,GAAG,CAAC,aAAa,CAAC;MAClC;MACA,IAAIU,KAAK,CAACY,wBAAwB,CAAC,CAAC,EAAE;QACpC,IAAIZ,KAAK,CAAC7B,IAAI,CAACkD,WAAW,EAAE;UAC1BrB,KAAK,GAAGA,KAAK,CAACV,GAAG,CAAC,aAAa,CAAC;QAClC,CAAC,MAAM,IACLvC,mBAAmB,IACnBiD,KAAK,CAAC7B,IAAI,CAACnC,MAAM,IACjBgE,KAAK,CAACV,GAAG,CAAC,QAAQ,CAAC,CAAClB,eAAe,CAAC,CAAC,EACrC;UACA4B,KAAK,CAACV,GAAG,CAAC,YAAY,CAAC,CAACS,OAAO,CAACG,IAAI,IAAI;YACtCxB,qBAAqB,CAACwB,IAAI,CAAC;YAC3Be,iBAAiB,CAACpB,GAAG,CAACK,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC,CAACnB,IAAI,CAACf,IAAI,EAAE,OAAO,CAAC;UAC7D,CAAC,CAAC;UACF;QACF;MACF;MAEA,IAAI4C,KAAK,CAACsB,qBAAqB,CAAC,CAAC,EAAE;QACjCF,IAAI,GAAG,SAAS;MAClB,CAAC,MAAM,IAAIpB,KAAK,CAACuB,kBAAkB,CAAC,CAAC,EAAE;QACrCH,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM,IAAIpB,KAAK,CAACwB,qBAAqB,CAAC;QAAEJ,IAAI,EAAE;MAAM,CAAC,CAAC,EAAE;QACvDA,IAAI,GAAG,KAAK;MACd,CAAC,MAAM,IAAIpB,KAAK,CAACwB,qBAAqB,CAAC,CAAC,EAAE;QACxCJ,IAAI,GAAG,OAAO;MAChB,CAAC,MAAM;QACL;MACF;IACF;IAEAK,MAAM,CAACC,IAAI,CAAC1B,KAAK,CAAC2B,0BAA0B,CAAC,CAAC,CAAC,CAAC5B,OAAO,CAAC3C,IAAI,IAAI;MAC9D6D,iBAAiB,CAACpB,GAAG,CAACzC,IAAI,EAAEgE,IAAI,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,MAAMQ,aAAa,GAAG,IAAI3C,GAAG,CAAC,CAAC;EAC/B,MAAM4C,gBAAgB,GAAIC,MAA8B,IAAK;IAC3D,MAAM1B,SAAS,GAAG0B,MAAM,CAAC3D,IAAI,CAACf,IAAI;IAClC,IAAItB,QAAQ,GAAG8F,aAAa,CAACtC,GAAG,CAACc,SAAS,CAAC;IAE3C,IAAI,CAACtE,QAAQ,EAAE;MAAA,IAAAiG,qBAAA,EAAAC,qBAAA;MAIb,MAAMZ,IAAuB,IAAAW,qBAAA,GAC3Bd,iBAAiB,CAAC3B,GAAG,CAACc,SAAS,CAAC,YAAA2B,qBAAA,IAAAC,qBAAA,GAC/Bd,YAAY,CAACe,UAAU,CAAC7B,SAAS,CAAC,qBAAlC4B,qBAAA,CAAoCZ,IAA0B;MAEjE,IAAIA,IAAI,KAAKc,SAAS,EAAE;QACtB,MAAMJ,MAAM,CAACjD,mBAAmB,CAC9B,oBAAoBuB,SAAS,2BAC/B,CAAC;MACH;MAEAtE,QAAQ,GAAG;QACTyE,KAAK,EAAE,EAAE;QACTa;MACF,CAAC;MACDQ,aAAa,CAAC/B,GAAG,CAACO,SAAS,EAAEtE,QAAQ,CAAC;IACxC;IACA,OAAOA,QAAQ;EACjB,CAAC;EAEDqF,eAAe,CAACpB,OAAO,CAACC,KAAK,IAAI;IAC/B,IACEA,KAAK,CAACY,wBAAwB,CAAC,CAAC,KAC/B7D,mBAAmB,IAAI,CAACiD,KAAK,CAAC7B,IAAI,CAACnC,MAAM,CAAC,EAC3C;MACA,IAAIgE,KAAK,CAAC7B,IAAI,CAACkD,WAAW,EAAE;QAC1B,MAAMA,WAAW,GAAGrB,KAAK,CAACV,GAAG,CAAC,aAAa,CAAC;QAC5C,MAAM6C,GAAG,GAAGd,WAAW,CAACe,8BAA8B,CAAC,CAAC;QACxDX,MAAM,CAACC,IAAI,CAACS,GAAG,CAAC,CAACpC,OAAO,CAAC3C,IAAI,IAAI;UAC/B,IAAIA,IAAI,KAAK,YAAY,EAAE;YACzB,MAAMiE,WAAW,CAACxC,mBAAmB,CACnC,8BACF,CAAC;UACH;UACAgD,gBAAgB,CAACM,GAAG,CAAC/E,IAAI,CAAC,CAAC,CAACmD,KAAK,CAACT,IAAI,CAAC1C,IAAI,CAAC;QAC9C,CAAC,CAAC;MACJ,CAAC,MAAM;QACL4C,KAAK,CAACV,GAAG,CAAC,YAAY,CAAC,CAACS,OAAO,CAACG,IAAI,IAAI;UACtC,MAAM1C,KAAK,GAAG0C,IAAI,CAACZ,GAAG,CAAC,OAAO,CAAC;UAC/B,MAAM+C,QAAQ,GAAGnC,IAAI,CAACZ,GAAG,CAAC,UAAU,CAAC;UACrC,MAAMsC,aAAa,GAAGC,gBAAgB,CAACrE,KAAK,CAAC;UAC7C,MAAMV,UAAU,GAAGkB,sBAAsB,CAACqE,QAAQ,EAAEhF,gBAAgB,CAAC;UAErE,IAAIP,UAAU,KAAK,YAAY,EAAE;YAC/B,MAAMuF,QAAQ,CAACxD,mBAAmB,CAAC,8BAA8B,CAAC;UACpE;UACA+C,aAAa,CAACrB,KAAK,CAACT,IAAI,CAAChD,UAAU,CAAC;QACtC,CAAC,CAAC;MACJ;IACF,CAAC,MAAM,IAAIkD,KAAK,CAACa,0BAA0B,CAAC,CAAC,EAAE;MAC7C,MAAMQ,WAAW,GAAGrB,KAAK,CAACV,GAAG,CAAC,aAAa,CAAC;MAC5C,IACE+B,WAAW,CAACC,qBAAqB,CAAC,CAAC,IACnCD,WAAW,CAACE,kBAAkB,CAAC,CAAC,EAChC;QACAM,gBAAgB,CAACR,WAAW,CAAC/B,GAAG,CAAC,IAAI,CAAC,CAAC,CAACiB,KAAK,CAACT,IAAI,CAAC,SAAS,CAAC;MAC/D,CAAC,MAAM;QAEL,MAAMuB,WAAW,CAACxC,mBAAmB,CACnC,uCACF,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACF,OAAO+C,aAAa;AACtB;AAKA,SAASrE,oBAAoBA,CAACV,WAAgC,EAAE;EAE9DA,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACS,OAAO,CAACC,KAAK,IAAI;IACvC,IAAI,CAACA,KAAK,CAACa,0BAA0B,CAAC,CAAC,EAAE;IACwB;MAAA,IAAAyB,qBAAA;MAE/D,CAAAA,qBAAA,GAAAtC,KAAK,CAACuC,sBAAsB,YAAAD,qBAAA,GAA5BtC,KAAK,CAACuC,sBAAsB,GAE1B5G,OAAO,CAAC,iBAAiB,CAAC,CAAC6G,QAAQ,CAACC,SAAS,CAACF,sBAAsB;IACxE;IACAvC,KAAK,CAACuC,sBAAsB,CAAC,CAAC;EAChC,CAAC,CAAC;AACJ;AAEA,SAAS5E,8BAA8BA,CAACd,WAAgC,EAAE;EACxEA,WAAW,CAACyC,GAAG,CAAC,MAAM,CAAC,CAACS,OAAO,CAACC,KAAK,IAAI;IACvC,IAAIA,KAAK,CAACC,mBAAmB,CAAC,CAAC,EAAE;MAC/BD,KAAK,CAAC0C,MAAM,CAAC,CAAC;IAChB,CAAC,MAAM,IAAI1C,KAAK,CAACY,wBAAwB,CAAC,CAAC,EAAE;MAC3C,IAAIZ,KAAK,CAAC7B,IAAI,CAACkD,WAAW,EAAE;QAE1BrB,KAAK,CAAC7B,IAAI,CAACkD,WAAW,CAACsB,WAAW,GAAG3C,KAAK,CAAC7B,IAAI,CAACwE,WAAW;QAC3D3C,KAAK,CAAC4C,WAAW,CAAC5C,KAAK,CAAC7B,IAAI,CAACkD,WAAW,CAAC;MAC3C,CAAC,MAAM;QACLrB,KAAK,CAAC0C,MAAM,CAAC,CAAC;MAChB;IACF,CAAC,MAAM,IAAI1C,KAAK,CAACa,0BAA0B,CAAC,CAAC,EAAE;MAE7C,MAAMQ,WAAW,GAAGrB,KAAK,CAACV,GAAG,CAAC,aAAa,CAAC;MAC5C,IACE+B,WAAW,CAACC,qBAAqB,CAAC,CAAC,IACnCD,WAAW,CAACE,kBAAkB,CAAC,CAAC,EAChC;QAEAF,WAAW,CAACsB,WAAW,GAAG3C,KAAK,CAAC7B,IAAI,CAACwE,WAAW;QAChD3C,KAAK,CAAC4C,WAAW,CAACvB,WAAW,CAAC;MAChC,CAAC,MAAM;QAEL,MAAMA,WAAW,CAACxC,mBAAmB,CACnC,uCACF,CAAC;MACH;IACF,CAAC,MAAM,IAAImB,KAAK,CAACW,sBAAsB,CAAC,CAAC,EAAE;MACzCX,KAAK,CAAC0C,MAAM,CAAC,CAAC;IAChB;EACF,CAAC,CAAC;AACJ", "ignoreList": []}